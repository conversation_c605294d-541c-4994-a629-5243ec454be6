import React, { useEffect, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";

interface SplashScreenProps {
  onComplete: () => void;
  isAppReady: boolean;
}

const SplashScreen: React.FC<SplashScreenProps> = ({
  onComplete,
  isAppReady,
}) => {
  const [showSplash, setShowSplash] = useState(true);

  useEffect(() => {
    // Timer minimo di 1 secondo
    const minTimer = setTimeout(() => {
      if (isAppReady) {
        setShowSplash(false);
        setTimeout(onComplete, 300); // Delay per transizione fluida
      }
    }, 1000);

    // Timer massimo di 3 secondi (fallback)
    const maxTimer = setTimeout(() => {
      setShowSplash(false);
      setTimeout(onComplete, 300);
    }, 3000);

    // Se l'app è pronta e è passato almeno 1 secondo
    if (isAppReady) {
      // Il minTimer gestirà la chiusura
    }

    return () => {
      clearTimeout(minTimer);
      clearTimeout(maxTimer);
    };
  }, [isAppReady, onComplete]);

  if (!showSplash) return null;

  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center"
      style={{
        background: `radial-gradient(
          ellipse at center,
          #fefce8 0%,
          #fef3c7 25%,
          #fed7aa 60%,
          #fed7d7 85%,
          #fecaca 100%
        )`,
        backgroundAttachment: "fixed",
      }}
    >
      {/* Pattern di sfondo con loghi */}
      <div
        className="fixed inset-0 pointer-events-none"
        style={{
          backgroundImage: 'url("/images/logos/logo_bastoni_compressed.png")',
          backgroundRepeat: "space",
          opacity: 0.08,
          filter:
            "sepia(80%) saturate(120%) hue-rotate(15deg) brightness(0.7) contrast(1.1)",
          transform: "rotate(8deg) scale(1.1)",
          transformOrigin: "center center",
          backgroundSize: "60px 60px",
          backgroundPosition: "0 0",
          zIndex: -1,
        }}
      />

      <div className="text-center relative z-10 animate-fade-in">
        {/* Logo principale */}
        <div className="mb-8">
          <img
            src="/images/logos/logo-new_nobg.png"
            alt="Marafone Romagnolo"
            className="w-32 h-32 mx-auto drop-shadow-2xl animate-gentle-bounce"
            style={{
              filter: "drop-shadow(0 12px 24px rgba(0,0,0,0.4))",
            }}
          />
        </div>

        {/* Titolo */}
        <h1
          className="text-4xl font-bold text-romagna-darkWood mb-2 drop-shadow-lg"
          style={{
            fontFamily: "'DynaPuff', cursive",
            textShadow: "2px 2px 4px rgba(0,0,0,0.3)",
          }}
        >
          Marafone
        </h1>
        <h2
          className="text-2xl font-semibold text-amber-700 drop-shadow-md"
          style={{
            fontFamily: "'DynaPuff', cursive",
            textShadow: "1px 1px 2px rgba(0,0,0,0.2)",
          }}
        >
          Romagnolo
        </h2>
      </div>

      {/* Stili CSS inline per le animazioni */}
      <style>{`
        @keyframes fade-in {
          from {
            opacity: 0;
            transform: translateY(20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        @keyframes gentle-bounce {
          0%, 20%, 50%, 80%, 100% {
            transform: translateY(0);
          }
          40% {
            transform: translateY(-10px);
          }
          60% {
            transform: translateY(-5px);
          }
        }

        .animate-fade-in {
          animation: fade-in 0.8s ease-out;
        }

        .animate-gentle-bounce {
          animation: gentle-bounce 2s ease-in-out infinite;
        }
      `}</style>
    </div>
  );
};

export default SplashScreen;
