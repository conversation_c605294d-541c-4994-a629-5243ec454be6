1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.eliazavatta.maraffa"
4    android:versionCode="27"
5    android:versionName="2.9" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10
11    <!-- Permissions -->
12
13    <uses-permission android:name="android.permission.INTERNET" />
13-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:61:5-67
13-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:61:22-64
14    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
14-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:62:5-79
14-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:62:22-76
15    <uses-permission android:name="android.permission.VIBRATE" />
15-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:63:5-66
15-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:63:22-63
16    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
16-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:64:5-79
16-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:64:22-76
17    <uses-permission android:name="com.android.vending.BILLING" />
17-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:65:5-67
17-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:65:22-64
18
19    <!-- Supporto per schermi grandi e diverse densità -->
20    <supports-screens
20-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:68:5-74:37
21        android:anyDensity="true"
21-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:73:9-34
22        android:largeScreens="true"
22-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:71:9-36
23        android:normalScreens="true"
23-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:70:9-37
24        android:resizeable="true"
24-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:74:9-34
25        android:smallScreens="true"
25-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:69:9-36
26        android:xlargeScreens="true" />
26-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:72:9-37
27
28    <!-- Supporto per diverse configurazioni hardware -->
29    <uses-feature
29-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:77:5-79:36
30        android:name="android.hardware.screen.portrait"
30-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:78:9-56
31        android:required="false" />
31-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:79:9-33
32    <uses-feature
32-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:80:5-82:36
33        android:name="android.hardware.screen.landscape"
33-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:81:9-57
34        android:required="false" />
34-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:82:9-33
35
36    <uses-permission android:name="android.permission.USE_CREDENTIALS" />
36-->[:codetrix-studio-capacitor-google-auth] C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\node_modules\@codetrix-studio\capacitor-google-auth\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-74
36-->[:codetrix-studio-capacitor-google-auth] C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\node_modules\@codetrix-studio\capacitor-google-auth\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-71
37
38    <queries>
38-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9845262327b0dd50c4969eecc4c29272\transformed\billing-7.1.1\AndroidManifest.xml:12:5-19:15
39        <intent>
39-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9845262327b0dd50c4969eecc4c29272\transformed\billing-7.1.1\AndroidManifest.xml:13:9-15:18
40            <action android:name="com.android.vending.billing.InAppBillingService.BIND" />
40-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9845262327b0dd50c4969eecc4c29272\transformed\billing-7.1.1\AndroidManifest.xml:14:13-91
40-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9845262327b0dd50c4969eecc4c29272\transformed\billing-7.1.1\AndroidManifest.xml:14:21-88
41        </intent>
42        <intent>
42-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9845262327b0dd50c4969eecc4c29272\transformed\billing-7.1.1\AndroidManifest.xml:16:9-18:18
43            <action android:name="com.google.android.apps.play.billingtestcompanion.BillingOverrideService.BIND" />
43-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9845262327b0dd50c4969eecc4c29272\transformed\billing-7.1.1\AndroidManifest.xml:17:13-116
43-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9845262327b0dd50c4969eecc4c29272\transformed\billing-7.1.1\AndroidManifest.xml:17:21-113
44        </intent>
45        <!-- For browser content -->
46        <intent>
46-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:38:9-44:18
47            <action android:name="android.intent.action.VIEW" />
47-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:29:17-69
47-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:29:25-66
48
49            <category android:name="android.intent.category.BROWSABLE" />
49-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:31:17-78
49-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:31:27-75
50
51            <data android:scheme="https" />
51-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:32:17-66
51-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:32:23-63
52        </intent> <!-- End of browser content -->
53        <!-- For CustomTabsService -->
54        <intent>
54-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:47:9-49:18
55            <action android:name="android.support.customtabs.action.CustomTabsService" />
55-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:48:13-90
55-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:48:21-87
56        </intent>
57    </queries>
58
59    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
59-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:27:5-82
59-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:27:22-79
60    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
60-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:28:5-88
60-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:28:22-85
61    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS" />
61-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:29:5-83
61-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:29:22-80
62    <uses-permission android:name="android.permission.WAKE_LOCK" />
62-->[com.google.android.gms:play-services-measurement:21.2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\503dac5d8d473c16443f07aa6a78d11e\transformed\play-services-measurement-21.2.2\AndroidManifest.xml:25:5-68
62-->[com.google.android.gms:play-services-measurement:21.2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\503dac5d8d473c16443f07aa6a78d11e\transformed\play-services-measurement-21.2.2\AndroidManifest.xml:25:22-65
63    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
63-->[com.google.android.gms:play-services-measurement:21.2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\503dac5d8d473c16443f07aa6a78d11e\transformed\play-services-measurement-21.2.2\AndroidManifest.xml:26:5-110
63-->[com.google.android.gms:play-services-measurement:21.2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\503dac5d8d473c16443f07aa6a78d11e\transformed\play-services-measurement-21.2.2\AndroidManifest.xml:26:22-107
64    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
64-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:5-77
64-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:22-74
65
66    <permission
66-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
67        android:name="com.eliazavatta.maraffa.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
67-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
68        android:protectionLevel="signature" />
68-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
69
70    <uses-permission android:name="com.eliazavatta.maraffa.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
70-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
70-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
71
72    <application
72-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:4:5-57:19
73        android:allowAudioPlaybackCapture="false"
73-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:11:9-50
74        android:allowBackup="true"
74-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:5:9-35
75        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
75-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
76        android:debuggable="true"
77        android:extractNativeLibs="false"
78        android:icon="@mipmap/ic_launcher"
78-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:6:9-43
79        android:label="@string/app_name"
79-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:7:9-41
80        android:roundIcon="@mipmap/ic_launcher_round"
80-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:8:9-54
81        android:supportsRtl="true"
81-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:9:9-35
82        android:theme="@style/AppTheme" >
82-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:10:9-40
83        <activity
83-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:13:9-35:20
84            android:name="com.eliazavatta.maraffa.MainActivity"
84-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:15:13-41
85            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|smallestScreenSize|screenLayout|uiMode|navigation"
85-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:14:13-140
86            android:exported="true"
86-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:19:13-36
87            android:label="@string/title_activity_main"
87-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:16:13-56
88            android:launchMode="singleTask"
88-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:18:13-44
89            android:resizeableActivity="true"
89-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:20:13-46
90            android:supportsPictureInPicture="false"
90-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:21:13-53
91            android:theme="@style/AppTheme.NoActionBarLaunch" >
91-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:17:13-62
92            <intent-filter>
92-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:22:13-25:29
93                <action android:name="android.intent.action.MAIN" />
93-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:23:17-69
93-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:23:25-66
94
95                <category android:name="android.intent.category.LAUNCHER" />
95-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:24:17-77
95-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:24:27-74
96            </intent-filter>
97
98            <!-- Deep link per OAuth redirect -->
99            <intent-filter android:autoVerify="true" >
99-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:28:13-33:29
99-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:28:28-53
100                <action android:name="android.intent.action.VIEW" />
100-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:29:17-69
100-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:29:25-66
101
102                <category android:name="android.intent.category.DEFAULT" />
102-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:30:17-76
102-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:30:27-73
103                <category android:name="android.intent.category.BROWSABLE" />
103-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:31:17-78
103-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:31:27-75
104
105                <data android:scheme="com.eliazavatta.maraffa" />
105-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:32:17-66
105-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:32:23-63
106            </intent-filter>
107        </activity>
108
109        <provider
110            android:name="androidx.core.content.FileProvider"
110-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:38:13-62
111            android:authorities="com.eliazavatta.maraffa.fileprovider"
111-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:39:13-64
112            android:exported="false"
112-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:40:13-37
113            android:grantUriPermissions="true" >
113-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:41:13-47
114            <meta-data
114-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:42:13-44:64
115                android:name="android.support.FILE_PROVIDER_PATHS"
115-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:43:17-67
116                android:resource="@xml/file_paths" />
116-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:44:17-51
117        </provider>
118
119        <!-- AdMob App ID -->
120        <meta-data
120-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:48:9-50:70
121            android:name="com.google.android.gms.ads.APPLICATION_ID"
121-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:49:13-69
122            android:value="ca-app-pub-3013811216506035~**********" />
122-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:50:13-67
123
124        <!-- Dichiarazione esplicita per l'uso dell'AD_ID -->
125        <meta-data
125-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:53:9-55:36
126            android:name="com.google.android.gms.ads.AD_ID_USAGE"
126-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:54:13-66
127            android:value="true" />
127-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:55:13-33
128
129        <activity
129-->[com.google.android.gms:play-services-auth:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ee9f356af7ade0d386b38fe5cf6881d\transformed\play-services-auth-18.1.0\AndroidManifest.xml:23:9-27:75
130            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
130-->[com.google.android.gms:play-services-auth:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ee9f356af7ade0d386b38fe5cf6881d\transformed\play-services-auth-18.1.0\AndroidManifest.xml:24:13-93
131            android:excludeFromRecents="true"
131-->[com.google.android.gms:play-services-auth:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ee9f356af7ade0d386b38fe5cf6881d\transformed\play-services-auth-18.1.0\AndroidManifest.xml:25:13-46
132            android:exported="false"
132-->[com.google.android.gms:play-services-auth:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ee9f356af7ade0d386b38fe5cf6881d\transformed\play-services-auth-18.1.0\AndroidManifest.xml:26:13-37
133            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
133-->[com.google.android.gms:play-services-auth:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ee9f356af7ade0d386b38fe5cf6881d\transformed\play-services-auth-18.1.0\AndroidManifest.xml:27:13-72
134        <!--
135            Service handling Google Sign-In user revocation. For apps that do not integrate with
136            Google Sign-In, this service will never be started.
137        -->
138        <service
138-->[com.google.android.gms:play-services-auth:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ee9f356af7ade0d386b38fe5cf6881d\transformed\play-services-auth-18.1.0\AndroidManifest.xml:33:9-37:51
139            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
139-->[com.google.android.gms:play-services-auth:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ee9f356af7ade0d386b38fe5cf6881d\transformed\play-services-auth-18.1.0\AndroidManifest.xml:34:13-89
140            android:exported="true"
140-->[com.google.android.gms:play-services-auth:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ee9f356af7ade0d386b38fe5cf6881d\transformed\play-services-auth-18.1.0\AndroidManifest.xml:35:13-36
141            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
141-->[com.google.android.gms:play-services-auth:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ee9f356af7ade0d386b38fe5cf6881d\transformed\play-services-auth-18.1.0\AndroidManifest.xml:36:13-107
142            android:visibleToInstantApps="true" />
142-->[com.google.android.gms:play-services-auth:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ee9f356af7ade0d386b38fe5cf6881d\transformed\play-services-auth-18.1.0\AndroidManifest.xml:37:13-48
143
144        <receiver
144-->[com.revenuecat.purchases:purchases-store-amazon:8.22.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f4f1bb42d5a19a9f997259548e65a28\transformed\purchases-store-amazon-8.22.0\AndroidManifest.xml:8:9-15:20
145            android:name="com.amazon.device.iap.ResponseReceiver"
145-->[com.revenuecat.purchases:purchases-store-amazon:8.22.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f4f1bb42d5a19a9f997259548e65a28\transformed\purchases-store-amazon-8.22.0\AndroidManifest.xml:9:13-66
146            android:exported="true"
146-->[com.revenuecat.purchases:purchases-store-amazon:8.22.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f4f1bb42d5a19a9f997259548e65a28\transformed\purchases-store-amazon-8.22.0\AndroidManifest.xml:10:13-36
147            android:permission="com.amazon.inapp.purchasing.Permission.NOTIFY" >
147-->[com.revenuecat.purchases:purchases-store-amazon:8.22.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f4f1bb42d5a19a9f997259548e65a28\transformed\purchases-store-amazon-8.22.0\AndroidManifest.xml:11:13-79
148            <intent-filter>
148-->[com.revenuecat.purchases:purchases-store-amazon:8.22.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f4f1bb42d5a19a9f997259548e65a28\transformed\purchases-store-amazon-8.22.0\AndroidManifest.xml:12:13-14:29
149                <action android:name="com.amazon.inapp.purchasing.NOTIFY" />
149-->[com.revenuecat.purchases:purchases-store-amazon:8.22.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f4f1bb42d5a19a9f997259548e65a28\transformed\purchases-store-amazon-8.22.0\AndroidManifest.xml:13:17-77
149-->[com.revenuecat.purchases:purchases-store-amazon:8.22.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f4f1bb42d5a19a9f997259548e65a28\transformed\purchases-store-amazon-8.22.0\AndroidManifest.xml:13:25-74
150            </intent-filter>
151        </receiver>
152
153        <activity
153-->[com.revenuecat.purchases:purchases:8.22.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e71e55f1b3f1b9ece32afceeeec4d6ac\transformed\purchases-8.22.0\AndroidManifest.xml:10:9-13:75
154            android:name="com.revenuecat.purchases.amazon.purchasing.ProxyAmazonBillingActivity"
154-->[com.revenuecat.purchases:purchases:8.22.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e71e55f1b3f1b9ece32afceeeec4d6ac\transformed\purchases-8.22.0\AndroidManifest.xml:11:13-97
155            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
155-->[com.revenuecat.purchases:purchases:8.22.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e71e55f1b3f1b9ece32afceeeec4d6ac\transformed\purchases-8.22.0\AndroidManifest.xml:12:13-96
156            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
156-->[com.revenuecat.purchases:purchases:8.22.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e71e55f1b3f1b9ece32afceeeec4d6ac\transformed\purchases-8.22.0\AndroidManifest.xml:13:13-72
157
158        <meta-data
158-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9845262327b0dd50c4969eecc4c29272\transformed\billing-7.1.1\AndroidManifest.xml:22:9-24:37
159            android:name="com.google.android.play.billingclient.version"
159-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9845262327b0dd50c4969eecc4c29272\transformed\billing-7.1.1\AndroidManifest.xml:23:13-73
160            android:value="7.1.1" />
160-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9845262327b0dd50c4969eecc4c29272\transformed\billing-7.1.1\AndroidManifest.xml:24:13-34
161
162        <activity
162-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9845262327b0dd50c4969eecc4c29272\transformed\billing-7.1.1\AndroidManifest.xml:26:9-30:75
163            android:name="com.android.billingclient.api.ProxyBillingActivity"
163-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9845262327b0dd50c4969eecc4c29272\transformed\billing-7.1.1\AndroidManifest.xml:27:13-78
164            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
164-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9845262327b0dd50c4969eecc4c29272\transformed\billing-7.1.1\AndroidManifest.xml:28:13-96
165            android:exported="false"
165-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9845262327b0dd50c4969eecc4c29272\transformed\billing-7.1.1\AndroidManifest.xml:29:13-37
166            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
166-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9845262327b0dd50c4969eecc4c29272\transformed\billing-7.1.1\AndroidManifest.xml:30:13-72
167        <activity
167-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9845262327b0dd50c4969eecc4c29272\transformed\billing-7.1.1\AndroidManifest.xml:31:9-35:75
168            android:name="com.android.billingclient.api.ProxyBillingActivityV2"
168-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9845262327b0dd50c4969eecc4c29272\transformed\billing-7.1.1\AndroidManifest.xml:32:13-80
169            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
169-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9845262327b0dd50c4969eecc4c29272\transformed\billing-7.1.1\AndroidManifest.xml:33:13-96
170            android:exported="false"
170-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9845262327b0dd50c4969eecc4c29272\transformed\billing-7.1.1\AndroidManifest.xml:34:13-37
171            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
171-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9845262327b0dd50c4969eecc4c29272\transformed\billing-7.1.1\AndroidManifest.xml:35:13-72
172        <activity
172-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3897ee7a3a7e64eb47ff9b7bb8256b24\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
173            android:name="com.google.android.gms.common.api.GoogleApiActivity"
173-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3897ee7a3a7e64eb47ff9b7bb8256b24\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
174            android:exported="false"
174-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3897ee7a3a7e64eb47ff9b7bb8256b24\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
175            android:theme="@android:style/Theme.Translucent.NoTitleBar" /> <!-- Include the AdActivity and InAppPurchaseActivity configChanges and themes. -->
175-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3897ee7a3a7e64eb47ff9b7bb8256b24\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
176        <activity
176-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:56:9-61:43
177            android:name="com.google.android.gms.ads.AdActivity"
177-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:57:13-65
178            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
178-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:58:13-122
179            android:exported="false"
179-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:59:13-37
180            android:theme="@android:style/Theme.Translucent" />
180-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:60:13-61
181
182        <provider
182-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:63:9-68:43
183            android:name="com.google.android.gms.ads.MobileAdsInitProvider"
183-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:64:13-76
184            android:authorities="com.eliazavatta.maraffa.mobileadsinitprovider"
184-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:65:13-73
185            android:exported="false"
185-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:66:13-37
186            android:initOrder="100" />
186-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:67:13-36
187
188        <service
188-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:70:9-74:43
189            android:name="com.google.android.gms.ads.AdService"
189-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:71:13-64
190            android:enabled="true"
190-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:72:13-35
191            android:exported="false" />
191-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:73:13-37
192
193        <activity
193-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:76:9-80:43
194            android:name="com.google.android.gms.ads.OutOfContextTestingActivity"
194-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:77:13-82
195            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
195-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:78:13-122
196            android:exported="false" />
196-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:79:13-37
197        <activity
197-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:81:9-88:43
198            android:name="com.google.android.gms.ads.NotificationHandlerActivity"
198-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:82:13-82
199            android:excludeFromRecents="true"
199-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:83:13-46
200            android:exported="false"
200-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:84:13-37
201            android:launchMode="singleTask"
201-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:85:13-44
202            android:taskAffinity=""
202-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:86:13-36
203            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
203-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:87:13-72
204
205        <property
205-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:90:9-92:62
206            android:name="android.adservices.AD_SERVICES_CONFIG"
206-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:91:13-65
207            android:resource="@xml/gma_ad_services_config" />
207-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:92:13-59
208
209        <receiver
209-->[com.google.android.gms:play-services-measurement:21.2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\503dac5d8d473c16443f07aa6a78d11e\transformed\play-services-measurement-21.2.2\AndroidManifest.xml:29:9-33:20
210            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
210-->[com.google.android.gms:play-services-measurement:21.2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\503dac5d8d473c16443f07aa6a78d11e\transformed\play-services-measurement-21.2.2\AndroidManifest.xml:30:13-85
211            android:enabled="true"
211-->[com.google.android.gms:play-services-measurement:21.2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\503dac5d8d473c16443f07aa6a78d11e\transformed\play-services-measurement-21.2.2\AndroidManifest.xml:31:13-35
212            android:exported="false" >
212-->[com.google.android.gms:play-services-measurement:21.2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\503dac5d8d473c16443f07aa6a78d11e\transformed\play-services-measurement-21.2.2\AndroidManifest.xml:32:13-37
213        </receiver>
214
215        <service
215-->[com.google.android.gms:play-services-measurement:21.2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\503dac5d8d473c16443f07aa6a78d11e\transformed\play-services-measurement-21.2.2\AndroidManifest.xml:35:9-38:40
216            android:name="com.google.android.gms.measurement.AppMeasurementService"
216-->[com.google.android.gms:play-services-measurement:21.2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\503dac5d8d473c16443f07aa6a78d11e\transformed\play-services-measurement-21.2.2\AndroidManifest.xml:36:13-84
217            android:enabled="true"
217-->[com.google.android.gms:play-services-measurement:21.2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\503dac5d8d473c16443f07aa6a78d11e\transformed\play-services-measurement-21.2.2\AndroidManifest.xml:37:13-35
218            android:exported="false" />
218-->[com.google.android.gms:play-services-measurement:21.2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\503dac5d8d473c16443f07aa6a78d11e\transformed\play-services-measurement-21.2.2\AndroidManifest.xml:38:13-37
219        <service
219-->[com.google.android.gms:play-services-measurement:21.2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\503dac5d8d473c16443f07aa6a78d11e\transformed\play-services-measurement-21.2.2\AndroidManifest.xml:39:9-43:72
220            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
220-->[com.google.android.gms:play-services-measurement:21.2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\503dac5d8d473c16443f07aa6a78d11e\transformed\play-services-measurement-21.2.2\AndroidManifest.xml:40:13-87
221            android:enabled="true"
221-->[com.google.android.gms:play-services-measurement:21.2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\503dac5d8d473c16443f07aa6a78d11e\transformed\play-services-measurement-21.2.2\AndroidManifest.xml:41:13-35
222            android:exported="false"
222-->[com.google.android.gms:play-services-measurement:21.2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\503dac5d8d473c16443f07aa6a78d11e\transformed\play-services-measurement-21.2.2\AndroidManifest.xml:42:13-37
223            android:permission="android.permission.BIND_JOB_SERVICE" />
223-->[com.google.android.gms:play-services-measurement:21.2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\503dac5d8d473c16443f07aa6a78d11e\transformed\play-services-measurement-21.2.2\AndroidManifest.xml:43:13-69
224        <service
224-->[com.google.android.gms:play-services-measurement-api:21.2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ae27ebad8b432bd935cbb5cc436e8c1c\transformed\play-services-measurement-api-21.2.2\AndroidManifest.xml:28:9-34:19
225            android:name="com.google.firebase.components.ComponentDiscoveryService"
225-->[com.google.android.gms:play-services-measurement-api:21.2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ae27ebad8b432bd935cbb5cc436e8c1c\transformed\play-services-measurement-api-21.2.2\AndroidManifest.xml:29:13-84
226            android:directBootAware="true"
226-->[com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b187c9076cc817781505e7d9e26441a\transformed\firebase-common-20.2.0\AndroidManifest.xml:34:13-43
227            android:exported="false" >
227-->[com.google.android.gms:play-services-measurement-api:21.2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ae27ebad8b432bd935cbb5cc436e8c1c\transformed\play-services-measurement-api-21.2.2\AndroidManifest.xml:30:13-37
228            <meta-data
228-->[com.google.android.gms:play-services-measurement-api:21.2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ae27ebad8b432bd935cbb5cc436e8c1c\transformed\play-services-measurement-api-21.2.2\AndroidManifest.xml:31:13-33:85
229                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
229-->[com.google.android.gms:play-services-measurement-api:21.2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ae27ebad8b432bd935cbb5cc436e8c1c\transformed\play-services-measurement-api-21.2.2\AndroidManifest.xml:32:17-139
230                android:value="com.google.firebase.components.ComponentRegistrar" />
230-->[com.google.android.gms:play-services-measurement-api:21.2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ae27ebad8b432bd935cbb5cc436e8c1c\transformed\play-services-measurement-api-21.2.2\AndroidManifest.xml:33:17-82
231            <meta-data
231-->[com.google.firebase:firebase-installations:17.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\19e36564445c6d2c9b6ee397781d806e\transformed\firebase-installations-17.0.1\AndroidManifest.xml:18:13-20:85
232                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
232-->[com.google.firebase:firebase-installations:17.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\19e36564445c6d2c9b6ee397781d806e\transformed\firebase-installations-17.0.1\AndroidManifest.xml:19:17-127
233                android:value="com.google.firebase.components.ComponentRegistrar" />
233-->[com.google.firebase:firebase-installations:17.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\19e36564445c6d2c9b6ee397781d806e\transformed\firebase-installations-17.0.1\AndroidManifest.xml:20:17-82
234        </service>
235
236        <provider
236-->[com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b187c9076cc817781505e7d9e26441a\transformed\firebase-common-20.2.0\AndroidManifest.xml:25:9-30:39
237            android:name="com.google.firebase.provider.FirebaseInitProvider"
237-->[com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b187c9076cc817781505e7d9e26441a\transformed\firebase-common-20.2.0\AndroidManifest.xml:26:13-77
238            android:authorities="com.eliazavatta.maraffa.firebaseinitprovider"
238-->[com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b187c9076cc817781505e7d9e26441a\transformed\firebase-common-20.2.0\AndroidManifest.xml:27:13-72
239            android:directBootAware="true"
239-->[com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b187c9076cc817781505e7d9e26441a\transformed\firebase-common-20.2.0\AndroidManifest.xml:28:13-43
240            android:exported="false"
240-->[com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b187c9076cc817781505e7d9e26441a\transformed\firebase-common-20.2.0\AndroidManifest.xml:29:13-37
241            android:initOrder="100" />
241-->[com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b187c9076cc817781505e7d9e26441a\transformed\firebase-common-20.2.0\AndroidManifest.xml:30:13-36
242
243        <meta-data
243-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\697a983ff8b6be23efe7df3e3bbc5a94\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
244            android:name="com.google.android.gms.version"
244-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\697a983ff8b6be23efe7df3e3bbc5a94\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
245            android:value="@integer/google_play_services_version" />
245-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\697a983ff8b6be23efe7df3e3bbc5a94\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
246
247        <provider
247-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
248            android:name="androidx.startup.InitializationProvider"
248-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
249            android:authorities="com.eliazavatta.maraffa.androidx-startup"
249-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
250            android:exported="false" >
250-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
251            <meta-data
251-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
252                android:name="androidx.emoji2.text.EmojiCompatInitializer"
252-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
253                android:value="androidx.startup" />
253-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
254            <meta-data
254-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:36:13-38:52
255                android:name="androidx.work.WorkManagerInitializer"
255-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:37:17-68
256                android:value="androidx.startup" />
256-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:38:17-49
257            <meta-data
257-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2086035d7e747a32c2be40e6ed7f404e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
258                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
258-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2086035d7e747a32c2be40e6ed7f404e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
259                android:value="androidx.startup" />
259-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2086035d7e747a32c2be40e6ed7f404e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
260            <meta-data
260-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
261                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
261-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
262                android:value="androidx.startup" />
262-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
263        </provider>
264
265        <uses-library
265-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b887b00b49cf9b23589e338bd66b82c\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
266            android:name="android.ext.adservices"
266-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b887b00b49cf9b23589e338bd66b82c\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
267            android:required="false" />
267-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b887b00b49cf9b23589e338bd66b82c\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
268
269        <service
269-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:41:9-46:35
270            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
270-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:42:13-88
271            android:directBootAware="false"
271-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:43:13-44
272            android:enabled="@bool/enable_system_alarm_service_default"
272-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:44:13-72
273            android:exported="false" />
273-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:45:13-37
274        <service
274-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:47:9-53:35
275            android:name="androidx.work.impl.background.systemjob.SystemJobService"
275-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:48:13-84
276            android:directBootAware="false"
276-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:49:13-44
277            android:enabled="@bool/enable_system_job_service_default"
277-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:50:13-70
278            android:exported="true"
278-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:51:13-36
279            android:permission="android.permission.BIND_JOB_SERVICE" />
279-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:52:13-69
280        <service
280-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:54:9-59:35
281            android:name="androidx.work.impl.foreground.SystemForegroundService"
281-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:55:13-81
282            android:directBootAware="false"
282-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:56:13-44
283            android:enabled="@bool/enable_system_foreground_service_default"
283-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:57:13-77
284            android:exported="false" />
284-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:58:13-37
285
286        <receiver
286-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:61:9-66:35
287            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
287-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:62:13-88
288            android:directBootAware="false"
288-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:63:13-44
289            android:enabled="true"
289-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:64:13-35
290            android:exported="false" />
290-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:65:13-37
291        <receiver
291-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:67:9-77:20
292            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
292-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:68:13-106
293            android:directBootAware="false"
293-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:69:13-44
294            android:enabled="false"
294-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:70:13-36
295            android:exported="false" >
295-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:71:13-37
296            <intent-filter>
296-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:73:13-76:29
297                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
297-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:17-87
297-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:25-84
298                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
298-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:17-90
298-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:25-87
299            </intent-filter>
300        </receiver>
301        <receiver
301-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:78:9-88:20
302            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
302-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:79:13-104
303            android:directBootAware="false"
303-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:80:13-44
304            android:enabled="false"
304-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:81:13-36
305            android:exported="false" >
305-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:82:13-37
306            <intent-filter>
306-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:84:13-87:29
307                <action android:name="android.intent.action.BATTERY_OKAY" />
307-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:17-77
307-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:25-74
308                <action android:name="android.intent.action.BATTERY_LOW" />
308-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:17-76
308-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:25-73
309            </intent-filter>
310        </receiver>
311        <receiver
311-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:89:9-99:20
312            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
312-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:90:13-104
313            android:directBootAware="false"
313-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:91:13-44
314            android:enabled="false"
314-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:92:13-36
315            android:exported="false" >
315-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:93:13-37
316            <intent-filter>
316-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:95:13-98:29
317                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
317-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:17-83
317-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:25-80
318                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
318-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:17-82
318-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:25-79
319            </intent-filter>
320        </receiver>
321        <receiver
321-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:100:9-109:20
322            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
322-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:101:13-103
323            android:directBootAware="false"
323-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:102:13-44
324            android:enabled="false"
324-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:103:13-36
325            android:exported="false" >
325-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:104:13-37
326            <intent-filter>
326-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:106:13-108:29
327                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
327-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:17-79
327-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:25-76
328            </intent-filter>
329        </receiver>
330        <receiver
330-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:110:9-121:20
331            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
331-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:111:13-88
332            android:directBootAware="false"
332-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:112:13-44
333            android:enabled="false"
333-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:113:13-36
334            android:exported="false" >
334-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:114:13-37
335            <intent-filter>
335-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:116:13-120:29
336                <action android:name="android.intent.action.BOOT_COMPLETED" />
336-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:17-79
336-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:25-76
337                <action android:name="android.intent.action.TIME_SET" />
337-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:17-73
337-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:25-70
338                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
338-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:17-81
338-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:25-78
339            </intent-filter>
340        </receiver>
341        <receiver
341-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:122:9-131:20
342            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
342-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:123:13-99
343            android:directBootAware="false"
343-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:124:13-44
344            android:enabled="@bool/enable_system_alarm_service_default"
344-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:125:13-72
345            android:exported="false" >
345-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:126:13-37
346            <intent-filter>
346-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:128:13-130:29
347                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
347-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:17-98
347-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:25-95
348            </intent-filter>
349        </receiver>
350        <receiver
350-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:132:9-142:20
351            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
351-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:133:13-78
352            android:directBootAware="false"
352-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:134:13-44
353            android:enabled="true"
353-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:135:13-35
354            android:exported="true"
354-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:136:13-36
355            android:permission="android.permission.DUMP" >
355-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:137:13-57
356            <intent-filter>
356-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:139:13-141:29
357                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
357-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:17-88
357-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:25-85
358            </intent-filter>
359        </receiver>
360        <receiver
360-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
361            android:name="androidx.profileinstaller.ProfileInstallReceiver"
361-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
362            android:directBootAware="false"
362-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
363            android:enabled="true"
363-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
364            android:exported="true"
364-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
365            android:permission="android.permission.DUMP" >
365-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
366            <intent-filter>
366-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
367                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
367-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
367-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
368            </intent-filter>
369            <intent-filter>
369-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
370                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
370-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
370-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
371            </intent-filter>
372            <intent-filter>
372-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
373                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
373-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
373-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
374            </intent-filter>
375            <intent-filter>
375-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
376                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
376-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
376-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
377            </intent-filter>
378        </receiver>
379
380        <service
380-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82b53e7ed6a17288256e97aa7a7e09b8\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
381            android:name="androidx.room.MultiInstanceInvalidationService"
381-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82b53e7ed6a17288256e97aa7a7e09b8\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
382            android:directBootAware="true"
382-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82b53e7ed6a17288256e97aa7a7e09b8\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
383            android:exported="false" />
383-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82b53e7ed6a17288256e97aa7a7e09b8\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
384        <service
384-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6623d766d878b79b1f5dea0a1a7f08b2\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:28:9-34:19
385            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
385-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6623d766d878b79b1f5dea0a1a7f08b2\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:29:13-103
386            android:exported="false" >
386-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6623d766d878b79b1f5dea0a1a7f08b2\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:30:13-37
387            <meta-data
387-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6623d766d878b79b1f5dea0a1a7f08b2\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:31:13-33:39
388                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
388-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6623d766d878b79b1f5dea0a1a7f08b2\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:32:17-94
389                android:value="cct" />
389-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6623d766d878b79b1f5dea0a1a7f08b2\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:33:17-36
390        </service>
391        <service
391-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5014b3ad63f7cceb01f18af1cfbd5a4e\transformed\transport-runtime-3.1.8\AndroidManifest.xml:26:9-30:19
392            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
392-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5014b3ad63f7cceb01f18af1cfbd5a4e\transformed\transport-runtime-3.1.8\AndroidManifest.xml:27:13-117
393            android:exported="false"
393-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5014b3ad63f7cceb01f18af1cfbd5a4e\transformed\transport-runtime-3.1.8\AndroidManifest.xml:28:13-37
394            android:permission="android.permission.BIND_JOB_SERVICE" >
394-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5014b3ad63f7cceb01f18af1cfbd5a4e\transformed\transport-runtime-3.1.8\AndroidManifest.xml:29:13-69
395        </service>
396
397        <receiver
397-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5014b3ad63f7cceb01f18af1cfbd5a4e\transformed\transport-runtime-3.1.8\AndroidManifest.xml:32:9-34:40
398            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
398-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5014b3ad63f7cceb01f18af1cfbd5a4e\transformed\transport-runtime-3.1.8\AndroidManifest.xml:33:13-132
399            android:exported="false" /> <!-- The activities will be merged into the manifest of the hosting app. -->
399-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5014b3ad63f7cceb01f18af1cfbd5a4e\transformed\transport-runtime-3.1.8\AndroidManifest.xml:34:13-37
400        <activity
400-->[com.google.android.play:core-common:2.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\832b1586368327240313f1a3abc0ec5d\transformed\core-common-2.0.2\AndroidManifest.xml:14:9-18:65
401            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
401-->[com.google.android.play:core-common:2.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\832b1586368327240313f1a3abc0ec5d\transformed\core-common-2.0.2\AndroidManifest.xml:15:13-93
402            android:exported="false"
402-->[com.google.android.play:core-common:2.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\832b1586368327240313f1a3abc0ec5d\transformed\core-common-2.0.2\AndroidManifest.xml:16:13-37
403            android:stateNotNeeded="true"
403-->[com.google.android.play:core-common:2.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\832b1586368327240313f1a3abc0ec5d\transformed\core-common-2.0.2\AndroidManifest.xml:17:13-42
404            android:theme="@style/Theme.PlayCore.Transparent" />
404-->[com.google.android.play:core-common:2.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\832b1586368327240313f1a3abc0ec5d\transformed\core-common-2.0.2\AndroidManifest.xml:18:13-62
405    </application>
406
407</manifest>
