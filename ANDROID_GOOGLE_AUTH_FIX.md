# 🔧 Fix Android Google Authentication - Errore Code 10

## Problema Identificato
L'autenticazione Google nativa fallisce con **errore code 10** (developer error) e fa fallback al browser, causando doppia autenticazione.

## Cause Principali
1. **SHA-1 fingerprint mancante** in Firebase Console
2. **Package name non corrispondente**
3. **google-services.json non aggiornato**
4. **attributionTag mancante** nel manifest (già risolto)

## ✅ Soluzioni Implementate

### 1. AndroidManifest.xml - Aggiunto attributionTag
```xml
<!-- Attribution tag per Android 11+ -->
<meta-data
    android:name="android.app.attribution"
    android:value="com.eliazavatta.maraffa" />

<!-- Google Play Services version -->
<meta-data
    android:name="com.google.android.gms.version"
    android:value="@integer/google_play_services_version" />
```

### 2. Migliorata gestione errori in authService.ts
- Aggiunta detection specifica per errore code 10
- Logging dettagliato per debug
- Prevenzione fallback browser per errori di configurazione

## 🚨 Azioni Richieste

### 1. Genera SHA-1 Fingerprint
```bash
# Debug keystore (per sviluppo)
keytool -list -v -keystore ~/.android/debug.keystore -alias androiddebugkey -storepass android -keypass android

# Release keystore (per produzione)
keytool -list -v -keystore path/to/your/release.keystore -alias your-alias
```

### 2. Configura Firebase Console
1. Vai su [Firebase Console](https://console.firebase.google.com)
2. Seleziona il progetto
3. Vai su **Project Settings** > **Your apps**
4. Seleziona l'app Android `com.eliazavatta.maraffa`
5. Aggiungi SHA-1 fingerprint nella sezione **SHA certificate fingerprints**
6. Scarica il nuovo `google-services.json`

### 3. Aggiorna google-services.json
1. Sostituisci `android/app/google-services.json` con il file scaricato
2. Verifica che contenga:
   ```json
   {
     "project_info": {
       "project_id": "your-project-id"
     },
     "client": [
       {
         "client_info": {
           "mobilesdk_app_id": "...",
           "android_client_info": {
             "package_name": "com.eliazavatta.maraffa"
           }
         },
         "oauth_client": [
           {
             "client_id": "your-android-client-id.apps.googleusercontent.com",
             "client_type": 1,
             "android_info": {
               "package_name": "com.eliazavatta.maraffa",
               "certificate_hash": "your-sha1-hash"
             }
           }
         ]
       }
     ]
   }
   ```

### 4. Verifica Configurazione Supabase
1. Vai su Supabase Dashboard > Authentication > Providers
2. Configura Google Provider con:
   - **Client ID**: Il client ID web da Firebase Console
   - **Client Secret**: Il client secret da Google Cloud Console
   - **Redirect URL**: `https://your-project.supabase.co/auth/v1/callback`

### 5. Test della Configurazione
```bash
# Clean e rebuild
npx cap clean android
npx cap sync android
npx cap build android

# Test su dispositivo fisico (non emulatore)
npx cap run android --target=device
```

## 🔍 Debug Commands

### Verifica SHA-1 nell'APK
```bash
# Estrai certificato dall'APK
unzip -p app-debug.apk META-INF/CERT.RSA | keytool -printcert

# Verifica fingerprint
keytool -list -printcert -file META-INF/CERT.RSA
```

### Log Android per Debug
```bash
# Filtra log Google Auth
adb logcat | grep -E "(GoogleAuth|OAuth|SignIn)"

# Log completi dell'app
adb logcat | grep "com.eliazavatta.maraffa"
```

## ⚠️ Note Importanti

1. **Usa dispositivo fisico** per testare - gli emulatori possono avere problemi con Google Play Services
2. **SHA-1 diversi** per debug e release - configura entrambi in Firebase
3. **Cache Google Play Services** - potrebbe essere necessario cancellare cache/dati di Google Play Services sul dispositivo
4. **Tempo di propagazione** - le modifiche in Firebase possono richiedere alcuni minuti per propagarsi

## 🎯 Risultato Atteso
Dopo aver applicato queste correzioni:
- ✅ Autenticazione Google nativa funziona senza errori
- ✅ Nessun fallback al browser
- ✅ Login con un solo tap sulla selezione account Google
- ✅ Nessun errore code 10 nei log
